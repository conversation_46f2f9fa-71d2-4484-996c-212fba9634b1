import { create } from 'zustand'

export interface Campaign {
  id: string
  merchantId: string
  influencerId: string
  serviceId: string
  title: string
  description: string
  productImages: string[]
  storeLink: string
  budget: number
  status: 'pending' | 'accepted' | 'rejected' | 'in_progress' | 'completed' | 'cancelled'
  createdAt: string
  updatedAt: string
  deadline?: string
  deliverables?: {
    id: string
    type: 'image' | 'video' | 'link'
    url: string
    description: string
    uploadedAt: string
  }[]
  messages?: {
    id: string
    senderId: string
    message: string
    timestamp: string
  }[]
  review?: {
    rating: number
    comment: string
    createdAt: string
  }
}

export interface CampaignState {
  campaigns: Campaign[]
  isLoading: boolean
  selectedCampaign: Campaign | null
  
  // Actions
  fetchCampaigns: (userId: string, userType: string) => Promise<void>
  createCampaign: (campaignData: Partial<Campaign>) => Promise<void>
  updateCampaignStatus: (campaignId: string, status: Campaign['status']) => Promise<void>
  addDeliverable: (campaignId: string, deliverable: Campaign['deliverables'][0]) => Promise<void>
  addMessage: (campaignId: string, message: Campaign['messages'][0]) => Promise<void>
  addReview: (campaignId: string, review: Campaign['review']) => Promise<void>
  selectCampaign: (campaign: Campaign | null) => void
}

export const useCampaignStore = create<CampaignState>((set, get) => ({
  campaigns: [],
  isLoading: false,
  selectedCampaign: null,

  fetchCampaigns: async (userId: string, userType: string) => {
    set({ isLoading: true })
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Mock campaigns data
      const mockCampaigns: Campaign[] = [
        {
          id: '1',
          merchantId: userType === 'merchant' ? userId : 'merchant1',
          influencerId: userType === 'influencer' ? userId : 'influencer1',
          serviceId: 'service1',
          title: 'ترويج منتج العناية بالبشرة',
          description: 'نريد ترويج منتج جديد للعناية بالبشرة للفئة العمرية 18-35',
          productImages: ['/api/placeholder/300/300'],
          storeLink: 'https://example.com/store',
          budget: 1500,
          status: 'pending',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          deadline: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
        },
        {
          id: '2',
          merchantId: userType === 'merchant' ? userId : 'merchant2',
          influencerId: userType === 'influencer' ? userId : 'influencer2',
          serviceId: 'service2',
          title: 'إعلان مطعم جديد',
          description: 'ترويج افتتاح مطعم جديد في الرياض',
          productImages: ['/api/placeholder/300/300'],
          storeLink: 'https://example.com/restaurant',
          budget: 2000,
          status: 'in_progress',
          createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          updatedAt: new Date().toISOString(),
          deadline: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString()
        }
      ]

      set({ campaigns: mockCampaigns, isLoading: false })
    } catch (error) {
      set({ isLoading: false })
      throw error
    }
  },

  createCampaign: async (campaignData: Partial<Campaign>) => {
    set({ isLoading: true })
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const newCampaign: Campaign = {
        id: Math.random().toString(36).substr(2, 9),
        merchantId: campaignData.merchantId || '',
        influencerId: campaignData.influencerId || '',
        serviceId: campaignData.serviceId || '',
        title: campaignData.title || '',
        description: campaignData.description || '',
        productImages: campaignData.productImages || [],
        storeLink: campaignData.storeLink || '',
        budget: campaignData.budget || 0,
        status: 'pending',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        ...campaignData
      }

      const { campaigns } = get()
      set({ 
        campaigns: [newCampaign, ...campaigns], 
        isLoading: false 
      })
    } catch (error) {
      set({ isLoading: false })
      throw error
    }
  },

  updateCampaignStatus: async (campaignId: string, status: Campaign['status']) => {
    const { campaigns } = get()
    const updatedCampaigns = campaigns.map(campaign =>
      campaign.id === campaignId
        ? { ...campaign, status, updatedAt: new Date().toISOString() }
        : campaign
    )
    
    set({ campaigns: updatedCampaigns })
  },

  addDeliverable: async (campaignId: string, deliverable: Campaign['deliverables'][0]) => {
    const { campaigns } = get()
    const updatedCampaigns = campaigns.map(campaign =>
      campaign.id === campaignId
        ? {
            ...campaign,
            deliverables: [...(campaign.deliverables || []), deliverable],
            updatedAt: new Date().toISOString()
          }
        : campaign
    )
    
    set({ campaigns: updatedCampaigns })
  },

  addMessage: async (campaignId: string, message: Campaign['messages'][0]) => {
    const { campaigns } = get()
    const updatedCampaigns = campaigns.map(campaign =>
      campaign.id === campaignId
        ? {
            ...campaign,
            messages: [...(campaign.messages || []), message],
            updatedAt: new Date().toISOString()
          }
        : campaign
    )
    
    set({ campaigns: updatedCampaigns })
  },

  addReview: async (campaignId: string, review: Campaign['review']) => {
    const { campaigns } = get()
    const updatedCampaigns = campaigns.map(campaign =>
      campaign.id === campaignId
        ? {
            ...campaign,
            review,
            status: 'completed' as const,
            updatedAt: new Date().toISOString()
          }
        : campaign
    )
    
    set({ campaigns: updatedCampaigns })
  },

  selectCampaign: (campaign: Campaign | null) => {
    set({ selectedCampaign: campaign })
  }
}))
