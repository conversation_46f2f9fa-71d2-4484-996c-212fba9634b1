'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Users, 
  TrendingUp, 
  DollarSign, 
  Shield,
  Eye,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  BarChart3,
  Calendar,
  CreditCard
} from 'lucide-react'
import Link from 'next/link'
import { useAuthStore } from '@/store/authStore'
import { useCampaignStore } from '@/store/campaignStore'
import { formatCurrency, campaignStatus, getStatusColor } from '@/lib/utils'

export default function AdminDashboard() {
  const { user } = useAuthStore()
  const { campaigns, fetchCampaigns, updateCampaignStatus, isLoading } = useCampaignStore()
  const [stats, setStats] = useState({
    totalUsers: 1247,
    totalMerchants: 523,
    totalInfluencers: 724,
    totalCampaigns: 0,
    pendingCampaigns: 0,
    activeCampaigns: 0,
    completedCampaigns: 0,
    totalRevenue: 0,
    platformFee: 0
  })

  useEffect(() => {
    if (user) {
      // Fetch all campaigns for admin view
      fetchCampaigns('admin', 'admin')
    }
  }, [user, fetchCampaigns])

  useEffect(() => {
    // Calculate stats from campaigns
    const totalCampaigns = campaigns.length
    const pendingCampaigns = campaigns.filter(c => c.status === 'pending').length
    const activeCampaigns = campaigns.filter(c => 
      ['accepted', 'in_progress'].includes(c.status)
    ).length
    const completedCampaigns = campaigns.filter(c => c.status === 'completed').length
    const totalRevenue = campaigns
      .filter(c => c.status === 'completed')
      .reduce((sum, c) => sum + c.budget, 0)
    const platformFee = totalRevenue * 0.05 // 5% platform fee

    setStats(prev => ({
      ...prev,
      totalCampaigns,
      pendingCampaigns,
      activeCampaigns,
      completedCampaigns,
      totalRevenue,
      platformFee
    }))
  }, [campaigns])

  const handleStatusUpdate = async (campaignId: string, newStatus: string) => {
    try {
      await updateCampaignStatus(campaignId, newStatus as any)
    } catch (error) {
      console.error('Failed to update campaign status:', error)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4" />
      case 'accepted':
      case 'in_progress':
        return <TrendingUp className="w-4 h-4" />
      case 'completed':
        return <CheckCircle className="w-4 h-4" />
      case 'rejected':
      case 'cancelled':
        return <XCircle className="w-4 h-4" />
      default:
        return <Clock className="w-4 h-4" />
    }
  }

  if (!user || user.type !== 'admin') {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white">غير مصرح لك بالوصول لهذه الصفحة</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <header className="bg-gray-800 border-b border-gray-700">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-white">لوحة تحكم المشرف</h1>
              <p className="text-gray-400">إدارة منصة المؤثرين</p>
            </div>
            
            <div className="flex items-center space-x-2 space-x-reverse">
              <div className="w-10 h-10 bg-gradient-to-r from-red-500 to-pink-600 rounded-xl flex items-center justify-center">
                <Shield className="w-6 h-6 text-white" />
              </div>
              <div>
                <p className="text-white font-medium">{user.name}</p>
                <p className="text-gray-400 text-sm">مشرف المنصة</p>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Stats Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
        >
          <div className="bg-gray-800 rounded-2xl p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-blue-600/20 rounded-xl flex items-center justify-center">
                <Users className="w-6 h-6 text-blue-400" />
              </div>
            </div>
            <h3 className="text-2xl font-bold text-white mb-1">{stats.totalUsers}</h3>
            <p className="text-gray-400 text-sm">إجمالي المستخدمين</p>
            <div className="flex items-center space-x-2 space-x-reverse text-xs text-gray-500 mt-2">
              <span>{stats.totalMerchants} تاجر</span>
              <span>•</span>
              <span>{stats.totalInfluencers} مؤثر</span>
            </div>
          </div>

          <div className="bg-gray-800 rounded-2xl p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-yellow-600/20 rounded-xl flex items-center justify-center">
                <AlertTriangle className="w-6 h-6 text-yellow-400" />
              </div>
            </div>
            <h3 className="text-2xl font-bold text-white mb-1">{stats.pendingCampaigns}</h3>
            <p className="text-gray-400 text-sm">حملات تحتاج مراجعة</p>
          </div>

          <div className="bg-gray-800 rounded-2xl p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-green-600/20 rounded-xl flex items-center justify-center">
                <TrendingUp className="w-6 h-6 text-green-400" />
              </div>
            </div>
            <h3 className="text-2xl font-bold text-white mb-1">{stats.activeCampaigns}</h3>
            <p className="text-gray-400 text-sm">حملات نشطة</p>
          </div>

          <div className="bg-gray-800 rounded-2xl p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-purple-600/20 rounded-xl flex items-center justify-center">
                <DollarSign className="w-6 h-6 text-purple-400" />
              </div>
            </div>
            <h3 className="text-2xl font-bold text-white mb-1">{formatCurrency(stats.platformFee)}</h3>
            <p className="text-gray-400 text-sm">عمولة المنصة</p>
          </div>
        </motion.div>

        {/* Revenue Overview */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="grid md:grid-cols-3 gap-6 mb-8"
        >
          <div className="bg-gray-800 rounded-2xl p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">إجمالي المعاملات</h3>
              <CreditCard className="w-6 h-6 text-indigo-400" />
            </div>
            <div className="text-3xl font-bold text-white mb-2">{formatCurrency(stats.totalRevenue)}</div>
            <p className="text-gray-400 text-sm">من {stats.completedCampaigns} حملة مكتملة</p>
          </div>

          <div className="bg-gray-800 rounded-2xl p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">معدل النجاح</h3>
              <BarChart3 className="w-6 h-6 text-green-400" />
            </div>
            <div className="text-3xl font-bold text-white mb-2">
              {stats.totalCampaigns > 0 ? Math.round((stats.completedCampaigns / stats.totalCampaigns) * 100) : 0}%
            </div>
            <p className="text-gray-400 text-sm">من إجمالي الحملات</p>
          </div>

          <div className="bg-gray-800 rounded-2xl p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">متوسط قيمة الحملة</h3>
              <TrendingUp className="w-6 h-6 text-purple-400" />
            </div>
            <div className="text-3xl font-bold text-white mb-2">
              {stats.completedCampaigns > 0 ? formatCurrency(stats.totalRevenue / stats.completedCampaigns) : formatCurrency(0)}
            </div>
            <p className="text-gray-400 text-sm">للحملات المكتملة</p>
          </div>
        </motion.div>

        {/* Pending Campaigns */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800 rounded-2xl border border-gray-700 mb-8"
        >
          <div className="p-6 border-b border-gray-700">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold text-white">الحملات المعلقة</h2>
              <span className="bg-yellow-600/20 text-yellow-400 px-3 py-1 rounded-full text-sm font-medium">
                {stats.pendingCampaigns} حملة
              </span>
            </div>
          </div>

          <div className="p-6">
            {isLoading ? (
              <div className="text-center py-8">
                <div className="text-gray-400">جاري التحميل...</div>
              </div>
            ) : campaigns.filter(c => c.status === 'pending').length === 0 ? (
              <div className="text-center py-8">
                <CheckCircle className="w-16 h-16 mx-auto mb-4 text-gray-600" />
                <h3 className="text-lg font-semibold text-gray-400 mb-2">لا توجد حملات معلقة</h3>
                <p className="text-gray-500">جميع الحملات تمت مراجعتها</p>
              </div>
            ) : (
              <div className="space-y-4">
                {campaigns.filter(c => c.status === 'pending').slice(0, 5).map((campaign) => (
                  <div
                    key={campaign.id}
                    className="flex items-center justify-between p-4 bg-gray-700/50 rounded-xl"
                  >
                    <div className="flex items-center space-x-4 space-x-reverse">
                      <div className="w-10 h-10 bg-yellow-600/20 rounded-xl flex items-center justify-center">
                        <Clock className="w-5 h-5 text-yellow-400" />
                      </div>
                      
                      <div>
                        <h3 className="font-semibold text-white">{campaign.title}</h3>
                        <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-400">
                          <span>{formatCurrency(campaign.budget)}</span>
                          <span className="flex items-center space-x-1 space-x-reverse">
                            <Calendar className="w-4 h-4" />
                            <span>{new Date(campaign.createdAt).toLocaleDateString('ar-SA')}</span>
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 space-x-reverse">
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => handleStatusUpdate(campaign.id, 'accepted')}
                        className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium"
                      >
                        قبول
                      </motion.button>
                      
                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        onClick={() => handleStatusUpdate(campaign.id, 'rejected')}
                        className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium"
                      >
                        رفض
                      </motion.button>
                      
                      <Link href={`/admin/campaigns/${campaign.id}`}>
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          className="w-10 h-10 bg-gray-600 hover:bg-gray-500 rounded-lg flex items-center justify-center text-gray-300 hover:text-white"
                        >
                          <Eye className="w-5 h-5" />
                        </motion.button>
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="grid md:grid-cols-4 gap-6"
        >
          <Link href="/admin/users">
            <div className="bg-gray-800 rounded-2xl p-6 border border-gray-700 hover:border-gray-600 transition-colors duration-200 cursor-pointer">
              <div className="w-12 h-12 bg-blue-600/20 rounded-xl flex items-center justify-center mb-4">
                <Users className="w-6 h-6 text-blue-400" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">إدارة المستخدمين</h3>
              <p className="text-gray-400 text-sm">عرض وإدارة التجار والمؤثرين</p>
            </div>
          </Link>

          <Link href="/admin/campaigns">
            <div className="bg-gray-800 rounded-2xl p-6 border border-gray-700 hover:border-gray-600 transition-colors duration-200 cursor-pointer">
              <div className="w-12 h-12 bg-green-600/20 rounded-xl flex items-center justify-center mb-4">
                <TrendingUp className="w-6 h-6 text-green-400" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">إدارة الحملات</h3>
              <p className="text-gray-400 text-sm">مراجعة ومتابعة جميع الحملات</p>
            </div>
          </Link>

          <Link href="/admin/payments">
            <div className="bg-gray-800 rounded-2xl p-6 border border-gray-700 hover:border-gray-600 transition-colors duration-200 cursor-pointer">
              <div className="w-12 h-12 bg-purple-600/20 rounded-xl flex items-center justify-center mb-4">
                <CreditCard className="w-6 h-6 text-purple-400" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">إدارة المدفوعات</h3>
              <p className="text-gray-400 text-sm">متابعة المدفوعات والتحويلات</p>
            </div>
          </Link>

          <Link href="/admin/analytics">
            <div className="bg-gray-800 rounded-2xl p-6 border border-gray-700 hover:border-gray-600 transition-colors duration-200 cursor-pointer">
              <div className="w-12 h-12 bg-indigo-600/20 rounded-xl flex items-center justify-center mb-4">
                <BarChart3 className="w-6 h-6 text-indigo-400" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">التحليلات</h3>
              <p className="text-gray-400 text-sm">إحصائيات وتقارير المنصة</p>
            </div>
          </Link>
        </motion.div>
      </div>
    </div>
  )
}
