import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "منصة المؤثرين السعودية | ربط التجار بالمؤثرين",
  description: "منصة سعودية موثوقة لربط التجار بالمؤثرين ومبدعي المحتوى. ضمان الحقوق والدفع الآمن.",
  keywords: "مؤثرين, تسويق, السعودية, إعلانات, محتوى, UGC",
  authors: [{ name: "منصة المؤثرين السعودية" }],
  viewport: "width=device-width, initial-scale=1, maximum-scale=1",
  themeColor: "#0f0f23",
  robots: "index, follow",
  openGraph: {
    title: "منصة المؤثرين السعودية",
    description: "منصة موثوقة لربط التجار بالمؤثرين في السعودية",
    type: "website",
    locale: "ar_SA",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
        <meta name="mobile-web-app-capable" content="yes" />
        <link rel="apple-touch-icon" href="/icon-192x192.png" />
        <link rel="manifest" href="/manifest.json" />
      </head>
      <body className={`${inter.variable} font-sans antialiased`}>
        <div className="min-h-screen bg-gray-900">
          {children}
        </div>
      </body>
    </html>
  );
}
