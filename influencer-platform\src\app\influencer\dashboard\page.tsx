'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Star, 
  TrendingUp, 
  DollarSign, 
  Calendar,
  Eye,
  Clock,
  CheckCircle,
  XCircle,
  Users,
  Settings,
  Upload,
  MessageSquare
} from 'lucide-react'
import Link from 'next/link'
import { useAuthStore } from '@/store/authStore'
import { useCampaignStore } from '@/store/campaignStore'
import { formatCurrency, campaignStatus, getStatusColor, formatNumber } from '@/lib/utils'

export default function InfluencerDashboard() {
  const { user } = useAuthStore()
  const { campaigns, fetchCampaigns, isLoading } = useCampaignStore()
  const [stats, setStats] = useState({
    totalRequests: 0,
    activeProjects: 0,
    completedProjects: 0,
    totalEarnings: 0,
    rating: 4.8,
    reviewsCount: 156
  })

  useEffect(() => {
    if (user) {
      fetchCampaigns(user.id, 'influencer')
    }
  }, [user, fetchCampaigns])

  useEffect(() => {
    // Calculate stats from campaigns
    const totalRequests = campaigns.length
    const activeProjects = campaigns.filter(c => 
      ['accepted', 'in_progress'].includes(c.status)
    ).length
    const completedProjects = campaigns.filter(c => c.status === 'completed').length
    const totalEarnings = campaigns
      .filter(c => c.status === 'completed')
      .reduce((sum, c) => sum + c.budget, 0)

    setStats(prev => ({
      ...prev,
      totalRequests,
      activeProjects,
      completedProjects,
      totalEarnings
    }))
  }, [campaigns])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4" />
      case 'accepted':
      case 'in_progress':
        return <TrendingUp className="w-4 h-4" />
      case 'completed':
        return <CheckCircle className="w-4 h-4" />
      case 'rejected':
      case 'cancelled':
        return <XCircle className="w-4 h-4" />
      default:
        return <Clock className="w-4 h-4" />
    }
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white">جاري التحميل...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <header className="bg-gray-800 border-b border-gray-700">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-white">مرحباً، {user.name}</h1>
              <p className="text-gray-400">لوحة تحكم المؤثر</p>
            </div>
            
            <div className="flex items-center space-x-4 space-x-reverse">
              <Link href="/influencer/profile/edit">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="bg-gray-700 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-xl flex items-center space-x-2 space-x-reverse"
                >
                  <Settings className="w-4 h-4" />
                  <span>تحديث الملف</span>
                </motion.button>
              </Link>
              
              <Link href="/influencer/portfolio">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-medium py-2 px-4 rounded-xl flex items-center space-x-2 space-x-reverse"
                >
                  <Upload className="w-4 h-4" />
                  <span>إدارة المعرض</span>
                </motion.button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Stats Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
        >
          <div className="bg-gray-800 rounded-2xl p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-blue-600/20 rounded-xl flex items-center justify-center">
                <MessageSquare className="w-6 h-6 text-blue-400" />
              </div>
            </div>
            <h3 className="text-2xl font-bold text-white mb-1">{stats.totalRequests}</h3>
            <p className="text-gray-400 text-sm">طلبات الإعلان</p>
          </div>

          <div className="bg-gray-800 rounded-2xl p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-yellow-600/20 rounded-xl flex items-center justify-center">
                <TrendingUp className="w-6 h-6 text-yellow-400" />
              </div>
            </div>
            <h3 className="text-2xl font-bold text-white mb-1">{stats.activeProjects}</h3>
            <p className="text-gray-400 text-sm">مشاريع نشطة</p>
          </div>

          <div className="bg-gray-800 rounded-2xl p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-green-600/20 rounded-xl flex items-center justify-center">
                <CheckCircle className="w-6 h-6 text-green-400" />
              </div>
            </div>
            <h3 className="text-2xl font-bold text-white mb-1">{stats.completedProjects}</h3>
            <p className="text-gray-400 text-sm">مشاريع مكتملة</p>
          </div>

          <div className="bg-gray-800 rounded-2xl p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-purple-600/20 rounded-xl flex items-center justify-center">
                <DollarSign className="w-6 h-6 text-purple-400" />
              </div>
            </div>
            <h3 className="text-2xl font-bold text-white mb-1">{formatCurrency(stats.totalEarnings)}</h3>
            <p className="text-gray-400 text-sm">إجمالي الأرباح</p>
          </div>
        </motion.div>

        {/* Profile Summary */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gray-800 rounded-2xl p-6 border border-gray-700 mb-8"
        >
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-white">ملخص الملف الشخصي</h2>
            <Link href="/influencer/profile/edit">
              <button className="text-indigo-400 hover:text-indigo-300 text-sm font-medium">
                تحديث
              </button>
            </Link>
          </div>

          <div className="grid md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="w-10 h-10 text-white" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-1">{user.name}</h3>
              <p className="text-gray-400 text-sm">مؤثر محتوى</p>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-400">التقييم</span>
                <div className="flex items-center space-x-1 space-x-reverse">
                  <Star className="w-4 h-4 text-yellow-400 fill-current" />
                  <span className="text-white font-medium">{stats.rating}</span>
                  <span className="text-gray-400 text-sm">({stats.reviewsCount})</span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-400">المشاريع المكتملة</span>
                <span className="text-white font-medium">{stats.completedProjects}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-400">معدل الاستجابة</span>
                <span className="text-green-400 font-medium">98%</span>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-gray-400">إنستغرام</span>
                <span className="text-white font-medium">{formatNumber(250000)}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-400">سناب شات</span>
                <span className="text-white font-medium">{formatNumber(180000)}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-gray-400">تيك توك</span>
                <span className="text-white font-medium">{formatNumber(320000)}</span>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Recent Requests */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800 rounded-2xl border border-gray-700"
        >
          <div className="p-6 border-b border-gray-700">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold text-white">الطلبات الأخيرة</h2>
              <Link href="/influencer/requests">
                <button className="text-indigo-400 hover:text-indigo-300 text-sm font-medium">
                  عرض الكل
                </button>
              </Link>
            </div>
          </div>

          <div className="p-6">
            {isLoading ? (
              <div className="text-center py-8">
                <div className="text-gray-400">جاري التحميل...</div>
              </div>
            ) : campaigns.length === 0 ? (
              <div className="text-center py-8">
                <MessageSquare className="w-16 h-16 mx-auto mb-4 text-gray-600" />
                <h3 className="text-lg font-semibold text-gray-400 mb-2">لا توجد طلبات بعد</h3>
                <p className="text-gray-500 mb-4">سيظهر هنا طلبات الإعلان الجديدة</p>
                <Link href="/influencer/profile/edit">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-xl"
                  >
                    تحديث الملف الشخصي
                  </motion.button>
                </Link>
              </div>
            ) : (
              <div className="space-y-4">
                {campaigns.slice(0, 5).map((campaign) => (
                  <div
                    key={campaign.id}
                    className="flex items-center justify-between p-4 bg-gray-700/50 rounded-xl hover:bg-gray-700/70 transition-colors duration-200"
                  >
                    <div className="flex items-center space-x-4 space-x-reverse">
                      <div className={`w-10 h-10 rounded-xl flex items-center justify-center ${getStatusColor(campaign.status).replace('text-', 'bg-').replace('400', '600/20')}`}>
                        {getStatusIcon(campaign.status)}
                      </div>
                      
                      <div>
                        <h3 className="font-semibold text-white">{campaign.title}</h3>
                        <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-400">
                          <span className={getStatusColor(campaign.status)}>
                            {campaignStatus[campaign.status as keyof typeof campaignStatus]}
                          </span>
                          <span className="flex items-center space-x-1 space-x-reverse">
                            <Calendar className="w-4 h-4" />
                            <span>{new Date(campaign.createdAt).toLocaleDateString('ar-SA')}</span>
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4 space-x-reverse">
                      <div className="text-left">
                        <div className="text-lg font-bold text-white">{formatCurrency(campaign.budget)}</div>
                        <div className="text-sm text-gray-400">المبلغ</div>
                      </div>
                      
                      <Link href={`/influencer/requests/${campaign.id}`}>
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          className="w-10 h-10 bg-gray-600 hover:bg-gray-500 rounded-xl flex items-center justify-center text-gray-300 hover:text-white"
                        >
                          <Eye className="w-5 h-5" />
                        </motion.button>
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="grid md:grid-cols-3 gap-6 mt-8"
        >
          <Link href="/influencer/profile/edit">
            <div className="bg-gray-800 rounded-2xl p-6 border border-gray-700 hover:border-gray-600 transition-colors duration-200 cursor-pointer">
              <div className="w-12 h-12 bg-indigo-600/20 rounded-xl flex items-center justify-center mb-4">
                <Settings className="w-6 h-6 text-indigo-400" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">تحديث الملف</h3>
              <p className="text-gray-400 text-sm">حدث معلوماتك وخدماتك وأسعارك</p>
            </div>
          </Link>

          <Link href="/influencer/portfolio">
            <div className="bg-gray-800 rounded-2xl p-6 border border-gray-700 hover:border-gray-600 transition-colors duration-200 cursor-pointer">
              <div className="w-12 h-12 bg-green-600/20 rounded-xl flex items-center justify-center mb-4">
                <Upload className="w-6 h-6 text-green-400" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">معرض الأعمال</h3>
              <p className="text-gray-400 text-sm">أضف وإدارة نماذج من أعمالك</p>
            </div>
          </Link>

          <Link href="/influencer/analytics">
            <div className="bg-gray-800 rounded-2xl p-6 border border-gray-700 hover:border-gray-600 transition-colors duration-200 cursor-pointer">
              <div className="w-12 h-12 bg-purple-600/20 rounded-xl flex items-center justify-center mb-4">
                <TrendingUp className="w-6 h-6 text-purple-400" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">الإحصائيات</h3>
              <p className="text-gray-400 text-sm">تتبع أداءك وأرباحك</p>
            </div>
          </Link>
        </motion.div>
      </div>
    </div>
  )
}
