'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Search, Filter, MapPin, Users, Star, ArrowRight, Instagram, Youtube, Twitter } from 'lucide-react'
import Link from 'next/link'
import { saudiCities, influencerCategories, formatNumber, formatCurrency } from '@/lib/utils'

// Mock influencer data
const mockInfluencers = [
  {
    id: '1',
    name: 'سارة أحمد',
    category: 'مؤثرين موضة',
    city: 'الرياض',
    avatar: '/api/placeholder/100/100',
    followers: {
      instagram: 250000,
      snapchat: 180000,
      tiktok: 320000
    },
    rating: 4.8,
    reviewsCount: 156,
    services: [
      { type: 'ريل إنستغرام', price: 1500 },
      { type: 'قصة إنستغرام', price: 800 },
      { type: 'فيديو تيك توك', price: 1200 }
    ],
    verified: true
  },
  {
    id: '2',
    name: 'محمد العتيبي',
    category: 'مؤثرين رياضة',
    city: 'جدة',
    avatar: '/api/placeholder/100/100',
    followers: {
      instagram: 180000,
      snapchat: 220000,
      youtube: 95000
    },
    rating: 4.9,
    reviewsCount: 203,
    services: [
      { type: 'فيديو يوتيوب', price: 3000 },
      { type: 'ريل إنستغرام', price: 1800 },
      { type: 'سنابات كاملة', price: 2200 }
    ],
    verified: true
  },
  {
    id: '3',
    name: 'نورا الشمري',
    category: 'مبدعي محتوى UGC',
    city: 'الدمام',
    avatar: '/api/placeholder/100/100',
    followers: {
      instagram: 85000,
      tiktok: 150000,
      snapchat: 120000
    },
    rating: 4.7,
    reviewsCount: 89,
    services: [
      { type: 'فيديو واحد', price: 800 },
      { type: 'صورة سناب واحدة', price: 400 },
      { type: 'فيديو تيك توك', price: 900 }
    ],
    verified: false
  }
]

export default function BrowsePage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCity, setSelectedCity] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [showFilters, setShowFilters] = useState(false)
  const [filteredInfluencers, setFilteredInfluencers] = useState(mockInfluencers)

  useEffect(() => {
    let filtered = mockInfluencers.filter(influencer => {
      const matchesSearch = influencer.name.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesCity = !selectedCity || influencer.city === selectedCity
      const matchesCategory = !selectedCategory || influencer.category === selectedCategory
      
      return matchesSearch && matchesCity && matchesCategory
    })
    
    setFilteredInfluencers(filtered)
  }, [searchTerm, selectedCity, selectedCategory])

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'instagram':
        return <Instagram className="w-4 h-4" />
      case 'youtube':
        return <Youtube className="w-4 h-4" />
      case 'twitter':
        return <Twitter className="w-4 h-4" />
      default:
        return <Users className="w-4 h-4" />
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-indigo-900">
      {/* Header */}
      <header className="sticky top-0 z-50 bg-gray-900/80 backdrop-blur-sm border-b border-gray-700">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Link href="/">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="flex items-center space-x-2 space-x-reverse text-gray-300 hover:text-white"
              >
                <ArrowRight className="w-5 h-5" />
                <span>العودة للرئيسية</span>
              </motion.button>
            </Link>
            
            <div className="text-center">
              <h1 className="text-xl font-bold text-white">تصفح المؤثرين</h1>
              <p className="text-sm text-gray-400">اكتشف أفضل المؤثرين في السعودية</p>
            </div>
            
            <Link href="/auth/login">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-xl font-medium"
              >
                تسجيل الدخول
              </motion.button>
            </Link>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Search and Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          {/* Search Bar */}
          <div className="relative mb-4">
            <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-4 pr-12 py-3 bg-gray-800 border border-gray-700 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500"
              placeholder="ابحث عن مؤثر..."
            />
          </div>

          {/* Filter Toggle */}
          <div className="flex items-center justify-between mb-4">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center space-x-2 space-x-reverse text-gray-300 hover:text-white"
            >
              <Filter className="w-5 h-5" />
              <span>فلترة النتائج</span>
            </button>
            
            <span className="text-gray-400 text-sm">
              {filteredInfluencers.length} مؤثر
            </span>
          </div>

          {/* Filters */}
          {showFilters && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="grid md:grid-cols-2 gap-4 p-4 bg-gray-800/50 rounded-xl border border-gray-700"
            >
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">المدينة</label>
                <select
                  value={selectedCity}
                  onChange={(e) => setSelectedCity(e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                >
                  <option value="">جميع المدن</option>
                  {saudiCities.map((city) => (
                    <option key={city} value={city}>{city}</option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">الفئة</label>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-lg text-white"
                >
                  <option value="">جميع الفئات</option>
                  {influencerCategories.map((category) => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>
            </motion.div>
          )}
        </motion.div>

        {/* Influencers Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredInfluencers.map((influencer, index) => (
            <motion.div
              key={influencer.id}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 border border-gray-700 hover:border-gray-600 transition-all duration-200"
            >
              {/* Profile Header */}
              <div className="flex items-center space-x-4 space-x-reverse mb-4">
                <div className="relative">
                  <img
                    src={influencer.avatar}
                    alt={influencer.name}
                    className="w-16 h-16 rounded-full object-cover"
                  />
                  {influencer.verified && (
                    <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                      <span className="text-white text-xs">✓</span>
                    </div>
                  )}
                </div>
                
                <div className="flex-1">
                  <h3 className="text-lg font-bold text-white">{influencer.name}</h3>
                  <p className="text-sm text-gray-400">{influencer.category}</p>
                  <div className="flex items-center space-x-1 space-x-reverse text-gray-400 text-sm">
                    <MapPin className="w-4 h-4" />
                    <span>{influencer.city}</span>
                  </div>
                </div>
              </div>

              {/* Rating */}
              <div className="flex items-center space-x-2 space-x-reverse mb-4">
                <div className="flex items-center space-x-1 space-x-reverse">
                  <Star className="w-4 h-4 text-yellow-400 fill-current" />
                  <span className="text-white font-medium">{influencer.rating}</span>
                </div>
                <span className="text-gray-400 text-sm">({influencer.reviewsCount} تقييم)</span>
              </div>

              {/* Followers */}
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-300 mb-2">المتابعون</h4>
                <div className="grid grid-cols-3 gap-2">
                  {Object.entries(influencer.followers).map(([platform, count]) => (
                    <div key={platform} className="bg-gray-700/50 rounded-lg p-2 text-center">
                      <div className="flex items-center justify-center mb-1">
                        {getPlatformIcon(platform)}
                      </div>
                      <span className="text-xs text-gray-300">{formatNumber(count)}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Services */}
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-300 mb-2">الخدمات</h4>
                <div className="space-y-2">
                  {influencer.services.slice(0, 2).map((service, idx) => (
                    <div key={idx} className="flex justify-between items-center text-sm">
                      <span className="text-gray-300">{service.type}</span>
                      <span className="text-green-400 font-medium">{formatCurrency(service.price)}</span>
                    </div>
                  ))}
                  {influencer.services.length > 2 && (
                    <p className="text-xs text-gray-400">+{influencer.services.length - 2} خدمة أخرى</p>
                  )}
                </div>
              </div>

              {/* CTA */}
              <Link href="/auth/register?type=merchant">
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-3 rounded-xl transition-colors duration-200"
                >
                  طلب إعلان
                </motion.button>
              </Link>
            </motion.div>
          ))}
        </div>

        {/* Empty State */}
        {filteredInfluencers.length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-center py-12"
          >
            <div className="text-gray-400 mb-4">
              <Users className="w-16 h-16 mx-auto mb-4 opacity-50" />
              <h3 className="text-xl font-semibold mb-2">لا توجد نتائج</h3>
              <p>جرب تغيير معايير البحث أو الفلترة</p>
            </div>
          </motion.div>
        )}

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="text-center mt-12 p-8 bg-gradient-to-r from-indigo-600/20 to-purple-600/20 rounded-2xl border border-indigo-500/30"
        >
          <h2 className="text-2xl font-bold text-white mb-4">جاهز لبدء حملتك الإعلانية؟</h2>
          <p className="text-gray-300 mb-6">انضم لآلاف التجار الذين يثقون بمنصتنا</p>
          <Link href="/auth/register?type=merchant">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-bold py-4 px-8 rounded-2xl shadow-lg"
            >
              ابدأ الآن مجاناً
            </motion.button>
          </Link>
        </motion.div>
      </div>
    </div>
  )
}
