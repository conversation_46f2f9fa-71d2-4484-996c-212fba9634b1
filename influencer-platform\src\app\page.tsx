'use client'

import { motion } from 'framer-motion'
import { Shield, Users, Star, ArrowLeft, CheckCircle, Smartphone, Globe, CreditCard } from 'lucide-react'
import Link from 'next/link'

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-indigo-900">
      {/* Header */}
      <header className="relative z-10 px-4 py-6">
        <div className="container mx-auto flex items-center justify-between">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex items-center space-x-3 space-x-reverse"
          >
            <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center">
              <Users className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-white">منصة المؤثرين</h1>
              <p className="text-xs text-gray-400">منصة سعودية موثوقة</p>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            className="flex items-center space-x-2 space-x-reverse"
          >
            <div className="flex items-center space-x-1 space-x-reverse text-green-400">
              <Shield className="w-4 h-4" />
              <span className="text-sm font-medium">موثوق</span>
            </div>
          </motion.div>
        </div>
      </header>

      {/* Hero Section */}
      <main className="px-4 py-8">
        <div className="container mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="mb-8"
          >
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-4 leading-tight">
              اربط علامتك التجارية
              <br />
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-400 to-purple-400">
                بأفضل المؤثرين
              </span>
            </h2>
            <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto leading-relaxed">
              منصة سعودية موثوقة تضمن حقوقك وتربطك بالمؤثرين ومبدعي المحتوى المناسبين لعلامتك التجارية
            </p>
          </motion.div>

          {/* Trust Indicators */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-12"
          >
            <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-4 border border-gray-700">
              <Shield className="w-8 h-8 text-green-400 mx-auto mb-2" />
              <h3 className="font-semibold text-white mb-1">ضمان الحقوق</h3>
              <p className="text-sm text-gray-400">حماية كاملة للطرفين</p>
            </div>
            <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-4 border border-gray-700">
              <CreditCard className="w-8 h-8 text-blue-400 mx-auto mb-2" />
              <h3 className="font-semibold text-white mb-1">دفع آمن</h3>
              <p className="text-sm text-gray-400">الأموال محجوزة حتى التسليم</p>
            </div>
            <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-4 border border-gray-700">
              <Globe className="w-8 h-8 text-purple-400 mx-auto mb-2" />
              <h3 className="font-semibold text-white mb-1">منصة سعودية</h3>
              <p className="text-sm text-gray-400">مصممة للسوق المحلي</p>
            </div>
            <div className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-4 border border-gray-700">
              <Smartphone className="w-8 h-8 text-indigo-400 mx-auto mb-2" />
              <h3 className="font-semibold text-white mb-1">تجربة جوالة</h3>
              <p className="text-sm text-gray-400">مصممة للهواتف الذكية</p>
            </div>
          </motion.div>

          {/* Main Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="space-y-4 mb-12"
          >
            <Link href="/auth/register?type=merchant">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="w-full max-w-md mx-auto block bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-bold py-4 px-8 rounded-2xl shadow-lg transition-all duration-200 mb-4"
              >
                <div className="flex items-center justify-center space-x-3 space-x-reverse">
                  <span className="text-lg">🚀</span>
                  <span>اطلق حملتك الإعلانية</span>
                </div>
                <p className="text-sm opacity-90 mt-1">للتجار وأصحاب الأعمال</p>
              </motion.button>
            </Link>

            <Link href="/auth/register?type=influencer">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="w-full max-w-md mx-auto block bg-gray-800 hover:bg-gray-700 text-white font-bold py-4 px-8 rounded-2xl border-2 border-gray-600 hover:border-gray-500 transition-all duration-200"
              >
                <div className="flex items-center justify-center space-x-3 space-x-reverse">
                  <span className="text-lg">⭐</span>
                  <span>انضم كمؤثر</span>
                </div>
                <p className="text-sm opacity-90 mt-1">للمؤثرين ومبدعي المحتوى</p>
              </motion.button>
            </Link>
          </motion.div>

          {/* Features Grid */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
            className="grid md:grid-cols-2 gap-6 mb-12"
          >
            <div className="bg-gray-800/30 backdrop-blur-sm rounded-2xl p-6 border border-gray-700">
              <h3 className="text-xl font-bold text-white mb-4">للتجار</h3>
              <ul className="space-y-3 text-right">
                <li className="flex items-center space-x-3 space-x-reverse">
                  <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                  <span className="text-gray-300">تصفح آلاف المؤثرين المتنوعين</span>
                </li>
                <li className="flex items-center space-x-3 space-x-reverse">
                  <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                  <span className="text-gray-300">فلترة حسب المدينة والفئة</span>
                </li>
                <li className="flex items-center space-x-3 space-x-reverse">
                  <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                  <span className="text-gray-300">دفع آمن بـ Apple Pay و Google Pay</span>
                </li>
                <li className="flex items-center space-x-3 space-x-reverse">
                  <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                  <span className="text-gray-300">متابعة حالة الحملة لحظياً</span>
                </li>
              </ul>
            </div>

            <div className="bg-gray-800/30 backdrop-blur-sm rounded-2xl p-6 border border-gray-700">
              <h3 className="text-xl font-bold text-white mb-4">للمؤثرين</h3>
              <ul className="space-y-3 text-right">
                <li className="flex items-center space-x-3 space-x-reverse">
                  <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                  <span className="text-gray-300">عرض خدماتك وأسعارك</span>
                </li>
                <li className="flex items-center space-x-3 space-x-reverse">
                  <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                  <span className="text-gray-300">استقبال طلبات مناسبة لك</span>
                </li>
                <li className="flex items-center space-x-3 space-x-reverse">
                  <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                  <span className="text-gray-300">ضمان الحصول على أجرك</span>
                </li>
                <li className="flex items-center space-x-3 space-x-reverse">
                  <CheckCircle className="w-5 h-5 text-green-400 flex-shrink-0" />
                  <span className="text-gray-300">بناء سمعة وتقييمات</span>
                </li>
              </ul>
            </div>
          </motion.div>

          {/* Guest Access */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1 }}
            className="text-center"
          >
            <p className="text-gray-400 mb-4">تريد استكشاف المنصة أولاً؟</p>
            <Link href="/browse">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="text-indigo-400 hover:text-indigo-300 font-medium underline underline-offset-4"
              >
                تصفح المؤثرين كضيف
              </motion.button>
            </Link>
          </motion.div>
        </div>
      </main>

      {/* Footer */}
      <footer className="px-4 py-8 border-t border-gray-800">
        <div className="container mx-auto text-center">
          <p className="text-gray-400 text-sm">
            © 2024 منصة المؤثرين السعودية. جميع الحقوق محفوظة.
          </p>
        </div>
      </footer>
    </div>
  )
}
