import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export interface User {
  id: string
  name: string
  email: string
  phone: string
  type: 'merchant' | 'influencer' | 'admin'
  avatar?: string
  isVerified: boolean
  createdAt: string
}

export interface Influencer extends User {
  type: 'influencer'
  category: string
  city: string
  bio: string
  followers: {
    snapchat?: number
    instagram?: number
    tiktok?: number
    youtube?: number
    twitter?: number
  }
  services: {
    id: string
    type: string
    platform: string
    price: number
    withEditing: boolean
    publishOnProfile: boolean
    description: string
  }[]
  portfolio: {
    id: string
    type: 'image' | 'video'
    url: string
    platform: string
    description: string
  }[]
  rating: number
  reviewsCount: number
  completedCampaigns: number
}

export interface Merchant extends User {
  type: 'merchant'
  businessName: string
  businessType: string
  city: string
  website?: string
  description: string
}

export interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  login: (email: string, password: string, userType: string) => Promise<void>
  register: (userData: any) => Promise<void>
  logout: () => void
  updateProfile: (data: Partial<User>) => void
  setUser: (user: User) => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,

      login: async (email: string, password: string, userType: string) => {
        set({ isLoading: true })
        
        try {
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1000))
          
          // Mock user data based on type
          const mockUser: User = {
            id: Math.random().toString(36).substr(2, 9),
            name: userType === 'merchant' ? 'محمد التاجر' : 'سارة المؤثرة',
            email,
            phone: '0501234567',
            type: userType as 'merchant' | 'influencer' | 'admin',
            isVerified: true,
            createdAt: new Date().toISOString()
          }

          set({ 
            user: mockUser, 
            isAuthenticated: true, 
            isLoading: false 
          })
        } catch (error) {
          set({ isLoading: false })
          throw error
        }
      },

      register: async (userData: any) => {
        set({ isLoading: true })
        
        try {
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1500))
          
          const newUser: User = {
            id: Math.random().toString(36).substr(2, 9),
            ...userData,
            isVerified: false,
            createdAt: new Date().toISOString()
          }

          set({ 
            user: newUser, 
            isAuthenticated: true, 
            isLoading: false 
          })
        } catch (error) {
          set({ isLoading: false })
          throw error
        }
      },

      logout: () => {
        set({ 
          user: null, 
          isAuthenticated: false 
        })
      },

      updateProfile: (data: Partial<User>) => {
        const { user } = get()
        if (user) {
          set({ 
            user: { ...user, ...data } 
          })
        }
      },

      setUser: (user: User) => {
        set({ 
          user, 
          isAuthenticated: true 
        })
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({ 
        user: state.user, 
        isAuthenticated: state.isAuthenticated 
      })
    }
  )
)
