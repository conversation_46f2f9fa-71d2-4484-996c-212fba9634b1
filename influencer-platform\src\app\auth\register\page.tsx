'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useRouter, useSearchParams } from 'next/navigation'
import { ArrowRight, User, Building, Star, Mail, Phone, MapPin, Eye, EyeOff } from 'lucide-react'
import Link from 'next/link'
import { useAuthStore } from '@/store/authStore'
import { saudiCities, influencerCategories } from '@/lib/utils'

export default function RegisterPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { register, isLoading } = useAuthStore()
  
  const [userType, setUserType] = useState<'merchant' | 'influencer'>('merchant')
  const [showPassword, setShowPassword] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    city: '',
    // Merchant specific
    businessName: '',
    businessType: '',
    website: '',
    description: '',
    // Influencer specific
    category: '',
    bio: ''
  })

  useEffect(() => {
    const type = searchParams.get('type')
    if (type === 'influencer' || type === 'merchant') {
      setUserType(type)
    }
  }, [searchParams])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (formData.password !== formData.confirmPassword) {
      alert('كلمات المرور غير متطابقة')
      return
    }

    try {
      await register({
        ...formData,
        type: userType
      })
      
      // Redirect based on user type
      if (userType === 'merchant') {
        router.push('/merchant/dashboard')
      } else {
        router.push('/influencer/dashboard')
      }
    } catch (error) {
      console.error('Registration failed:', error)
      alert('فشل في التسجيل. حاول مرة أخرى.')
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-indigo-900 py-8">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <Link href="/">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex items-center space-x-2 space-x-reverse text-gray-300 hover:text-white"
            >
              <ArrowRight className="w-5 h-5" />
              <span>العودة للرئيسية</span>
            </motion.button>
          </Link>
          
          <div className="text-center">
            <h1 className="text-2xl font-bold text-white">إنشاء حساب جديد</h1>
            <p className="text-gray-400">انضم لمنصة المؤثرين السعودية</p>
          </div>
          
          <div className="w-20"></div>
        </div>

        {/* User Type Selection */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-md mx-auto mb-8"
        >
          <div className="bg-gray-800 rounded-2xl p-2 flex">
            <button
              onClick={() => setUserType('merchant')}
              className={`flex-1 py-3 px-4 rounded-xl font-medium transition-all duration-200 ${
                userType === 'merchant'
                  ? 'bg-indigo-600 text-white'
                  : 'text-gray-400 hover:text-white'
              }`}
            >
              <Building className="w-5 h-5 mx-auto mb-1" />
              <span className="text-sm">تاجر</span>
            </button>
            <button
              onClick={() => setUserType('influencer')}
              className={`flex-1 py-3 px-4 rounded-xl font-medium transition-all duration-200 ${
                userType === 'influencer'
                  ? 'bg-indigo-600 text-white'
                  : 'text-gray-400 hover:text-white'
              }`}
            >
              <Star className="w-5 h-5 mx-auto mb-1" />
              <span className="text-sm">مؤثر</span>
            </button>
          </div>
        </motion.div>

        {/* Registration Form */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="max-w-lg mx-auto"
        >
          <form onSubmit={handleSubmit} className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 border border-gray-700">
            {/* Basic Information */}
            <div className="space-y-4 mb-6">
              <h3 className="text-lg font-semibold text-white mb-4">المعلومات الأساسية</h3>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">الاسم الكامل</label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="input"
                  placeholder="أدخل اسمك الكامل"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">البريد الإلكتروني</label>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="input"
                  placeholder="<EMAIL>"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">رقم الجوال</label>
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  className="input"
                  placeholder="05xxxxxxxx"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">المدينة</label>
                <select
                  name="city"
                  value={formData.city}
                  onChange={handleInputChange}
                  className="input"
                  required
                >
                  <option value="">اختر المدينة</option>
                  {saudiCities.map((city) => (
                    <option key={city} value={city}>{city}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">كلمة المرور</label>
                <div className="relative">
                  <input
                    type={showPassword ? 'text' : 'password'}
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    className="input pr-12"
                    placeholder="أدخل كلمة مرور قوية"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                  >
                    {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">تأكيد كلمة المرور</label>
                <input
                  type="password"
                  name="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  className="input"
                  placeholder="أعد إدخال كلمة المرور"
                  required
                />
              </div>
            </div>

            {/* Type-specific fields */}
            {userType === 'merchant' ? (
              <div className="space-y-4 mb-6">
                <h3 className="text-lg font-semibold text-white mb-4">معلومات العمل</h3>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">اسم النشاط التجاري</label>
                  <input
                    type="text"
                    name="businessName"
                    value={formData.businessName}
                    onChange={handleInputChange}
                    className="input"
                    placeholder="اسم متجرك أو شركتك"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">نوع النشاط</label>
                  <input
                    type="text"
                    name="businessType"
                    value={formData.businessType}
                    onChange={handleInputChange}
                    className="input"
                    placeholder="مثال: أزياء، إلكترونيات، مطاعم"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">الموقع الإلكتروني (اختياري)</label>
                  <input
                    type="url"
                    name="website"
                    value={formData.website}
                    onChange={handleInputChange}
                    className="input"
                    placeholder="https://example.com"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">وصف النشاط</label>
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    className="input min-h-[100px] resize-none"
                    placeholder="اكتب وصفاً مختصراً عن نشاطك التجاري"
                    required
                  />
                </div>
              </div>
            ) : (
              <div className="space-y-4 mb-6">
                <h3 className="text-lg font-semibold text-white mb-4">معلومات المؤثر</h3>
                
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">فئة المحتوى</label>
                  <select
                    name="category"
                    value={formData.category}
                    onChange={handleInputChange}
                    className="input"
                    required
                  >
                    <option value="">اختر فئة المحتوى</option>
                    {influencerCategories.map((category) => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">نبذة عنك</label>
                  <textarea
                    name="bio"
                    value={formData.bio}
                    onChange={handleInputChange}
                    className="input min-h-[100px] resize-none"
                    placeholder="اكتب نبذة مختصرة عن نفسك ونوع المحتوى الذي تقدمه"
                    required
                  />
                </div>
              </div>
            )}

            {/* Submit Button */}
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              type="submit"
              disabled={isLoading}
              className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-bold py-4 px-8 rounded-2xl shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'جاري إنشاء الحساب...' : 'إنشاء الحساب'}
            </motion.button>

            {/* Login Link */}
            <div className="text-center mt-6">
              <p className="text-gray-400">
                لديك حساب بالفعل؟{' '}
                <Link href="/auth/login" className="text-indigo-400 hover:text-indigo-300 font-medium">
                  تسجيل الدخول
                </Link>
              </p>
            </div>
          </form>
        </motion.div>
      </div>
    </div>
  )
}
