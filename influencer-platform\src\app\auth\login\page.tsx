'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { ArrowRight, Mail, Eye, EyeOff, Building, Star, Shield } from 'lucide-react'
import Link from 'next/link'
import { useAuthStore } from '@/store/authStore'

export default function LoginPage() {
  const router = useRouter()
  const { login, isLoading } = useAuthStore()
  
  const [showPassword, setShowPassword] = useState(false)
  const [userType, setUserType] = useState<'merchant' | 'influencer' | 'admin'>('merchant')
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      await login(formData.email, formData.password, userType)
      
      // Redirect based on user type
      switch (userType) {
        case 'merchant':
          router.push('/merchant/dashboard')
          break
        case 'influencer':
          router.push('/influencer/dashboard')
          break
        case 'admin':
          router.push('/admin/dashboard')
          break
      }
    } catch (error) {
      console.error('Login failed:', error)
      alert('فشل في تسجيل الدخول. تحقق من البيانات وحاول مرة أخرى.')
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-indigo-900 py-8">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <Link href="/">
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="flex items-center space-x-2 space-x-reverse text-gray-300 hover:text-white"
            >
              <ArrowRight className="w-5 h-5" />
              <span>العودة للرئيسية</span>
            </motion.button>
          </Link>
          
          <div className="text-center">
            <h1 className="text-2xl font-bold text-white">تسجيل الدخول</h1>
            <p className="text-gray-400">مرحباً بك في منصة المؤثرين</p>
          </div>
          
          <div className="w-20"></div>
        </div>

        {/* Login Form */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-md mx-auto"
        >
          <form onSubmit={handleSubmit} className="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 border border-gray-700">
            {/* User Type Selection */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-300 mb-3">نوع الحساب</label>
              <div className="grid grid-cols-3 gap-2 bg-gray-800 rounded-xl p-2">
                <button
                  type="button"
                  onClick={() => setUserType('merchant')}
                  className={`py-3 px-2 rounded-lg font-medium transition-all duration-200 text-sm ${
                    userType === 'merchant'
                      ? 'bg-indigo-600 text-white'
                      : 'text-gray-400 hover:text-white'
                  }`}
                >
                  <Building className="w-4 h-4 mx-auto mb-1" />
                  <span>تاجر</span>
                </button>
                <button
                  type="button"
                  onClick={() => setUserType('influencer')}
                  className={`py-3 px-2 rounded-lg font-medium transition-all duration-200 text-sm ${
                    userType === 'influencer'
                      ? 'bg-indigo-600 text-white'
                      : 'text-gray-400 hover:text-white'
                  }`}
                >
                  <Star className="w-4 h-4 mx-auto mb-1" />
                  <span>مؤثر</span>
                </button>
                <button
                  type="button"
                  onClick={() => setUserType('admin')}
                  className={`py-3 px-2 rounded-lg font-medium transition-all duration-200 text-sm ${
                    userType === 'admin'
                      ? 'bg-indigo-600 text-white'
                      : 'text-gray-400 hover:text-white'
                  }`}
                >
                  <Shield className="w-4 h-4 mx-auto mb-1" />
                  <span>مشرف</span>
                </button>
              </div>
            </div>

            {/* Email Field */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-300 mb-2">البريد الإلكتروني</label>
              <div className="relative">
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="input pl-12"
                  placeholder="<EMAIL>"
                  required
                />
                <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
              </div>
            </div>

            {/* Password Field */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-300 mb-2">كلمة المرور</label>
              <div className="relative">
                <input
                  type={showPassword ? 'text' : 'password'}
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  className="input pl-12 pr-12"
                  placeholder="أدخل كلمة المرور"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
            </div>

            {/* Demo Accounts Info */}
            <div className="mb-6 p-4 bg-blue-900/20 border border-blue-700/30 rounded-xl">
              <h4 className="text-sm font-medium text-blue-300 mb-2">حسابات تجريبية:</h4>
              <div className="text-xs text-blue-200 space-y-1">
                <p>تاجر: <EMAIL> / password123</p>
                <p>مؤثر: <EMAIL> / password123</p>
                <p>مشرف: <EMAIL> / password123</p>
              </div>
            </div>

            {/* Submit Button */}
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              type="submit"
              disabled={isLoading}
              className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-bold py-4 px-8 rounded-2xl shadow-lg transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed mb-4"
            >
              {isLoading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
            </motion.button>

            {/* Forgot Password */}
            <div className="text-center mb-4">
              <Link href="/auth/forgot-password" className="text-sm text-indigo-400 hover:text-indigo-300">
                نسيت كلمة المرور؟
              </Link>
            </div>

            {/* Register Link */}
            <div className="text-center">
              <p className="text-gray-400 text-sm">
                ليس لديك حساب؟{' '}
                <Link href="/auth/register" className="text-indigo-400 hover:text-indigo-300 font-medium">
                  إنشاء حساب جديد
                </Link>
              </p>
            </div>
          </form>

          {/* Guest Access */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="text-center mt-6"
          >
            <p className="text-gray-400 mb-3">أو</p>
            <Link href="/browse">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="text-indigo-400 hover:text-indigo-300 font-medium underline underline-offset-4"
              >
                تصفح المنصة كضيف
              </motion.button>
            </Link>
          </motion.div>
        </motion.div>
      </div>
    </div>
  )
}
