import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Saudi cities and regions
export const saudiCities = [
  'الرياض',
  'جدة', 
  'مكة المكرمة',
  'المدينة المنورة',
  'الدمام',
  'الخبر',
  'الظهران',
  'تبوك',
  'بريدة',
  'خميس مشيط',
  'حائل',
  'نجران',
  'الطائف',
  'الجبيل',
  'ينبع',
  'أبها',
  'عرعر',
  'سكاكا',
  'جازان',
  'القطيف',
  'الأحساء',
  'الباحة',
  'القصيم',
  'عسير',
  'الحدود الشمالية',
  'الجوف'
]

// Platform categories
export const influencerCategories = [
  'مشاهير',
  'مبدعي محتوى UGC',
  'مؤثرين رياضة',
  'مؤثرين طبخ',
  'مؤثرين موضة',
  'مؤثرين تقنية',
  'مؤثرين سفر',
  'مؤثرين صحة',
  'مؤثرين تعليم',
  'مؤثرين ترفيه'
]

// Service types
export const serviceTypes = [
  'زيارة المحل',
  'سنابات كاملة',
  'صورة سناب واحدة',
  'فيديو واحد',
  'ريل إنستغرام',
  'فيديو تيك توك',
  'فيديو يوتيوب',
  'قصة إنستغرام',
  'منشور إنستغرام',
  'تغريدة تويتر'
]

// Platform types
export const platformTypes = [
  'سناب شات',
  'إنستغرام',
  'تيك توك',
  'يوتيوب',
  'تويتر',
  'لينكد إن'
]

// Format numbers in Arabic
export function formatNumber(num: number): string {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'م'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'ك'
  }
  return num.toString()
}

// Format currency in SAR
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('ar-SA', {
    style: 'currency',
    currency: 'SAR',
    minimumFractionDigits: 0
  }).format(amount)
}

// Validate Saudi phone number
export function validateSaudiPhone(phone: string): boolean {
  const saudiPhoneRegex = /^(05|5)[0-9]{8}$/
  return saudiPhoneRegex.test(phone.replace(/\s+/g, ''))
}

// Validate email
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// Generate unique ID
export function generateId(): string {
  return Math.random().toString(36).substr(2, 9)
}

// Get user type from path
export function getUserTypeFromPath(path: string): 'merchant' | 'influencer' | 'admin' | null {
  if (path.includes('/merchant')) return 'merchant'
  if (path.includes('/influencer')) return 'influencer'
  if (path.includes('/admin')) return 'admin'
  return null
}

// Campaign status in Arabic
export const campaignStatus = {
  pending: 'قيد المراجعة',
  accepted: 'مقبول',
  rejected: 'مرفوض',
  in_progress: 'قيد التنفيذ',
  completed: 'مكتمل',
  cancelled: 'ملغي'
}

// Get status color
export function getStatusColor(status: string): string {
  switch (status) {
    case 'pending':
      return 'text-yellow-400'
    case 'accepted':
      return 'text-green-400'
    case 'rejected':
      return 'text-red-400'
    case 'in_progress':
      return 'text-blue-400'
    case 'completed':
      return 'text-green-500'
    case 'cancelled':
      return 'text-gray-400'
    default:
      return 'text-gray-400'
  }
}
