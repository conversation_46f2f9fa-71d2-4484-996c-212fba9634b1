'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  BarChart3, 
  Plus, 
  Eye, 
  Clock, 
  CheckCircle, 
  XCircle, 
  TrendingUp,
  Users,
  DollarSign,
  Calendar
} from 'lucide-react'
import Link from 'next/link'
import { useAuthStore } from '@/store/authStore'
import { useCampaignStore } from '@/store/campaignStore'
import { formatCurrency, campaignStatus, getStatusColor } from '@/lib/utils'

export default function MerchantDashboard() {
  const { user } = useAuthStore()
  const { campaigns, fetchCampaigns, isLoading } = useCampaignStore()
  const [stats, setStats] = useState({
    totalCampaigns: 0,
    activeCampaigns: 0,
    completedCampaigns: 0,
    totalSpent: 0
  })

  useEffect(() => {
    if (user) {
      fetchCampaigns(user.id, 'merchant')
    }
  }, [user, fetchCampaigns])

  useEffect(() => {
    // Calculate stats from campaigns
    const totalCampaigns = campaigns.length
    const activeCampaigns = campaigns.filter(c => 
      ['pending', 'accepted', 'in_progress'].includes(c.status)
    ).length
    const completedCampaigns = campaigns.filter(c => c.status === 'completed').length
    const totalSpent = campaigns
      .filter(c => c.status === 'completed')
      .reduce((sum, c) => sum + c.budget, 0)

    setStats({
      totalCampaigns,
      activeCampaigns,
      completedCampaigns,
      totalSpent
    })
  }, [campaigns])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4" />
      case 'accepted':
      case 'in_progress':
        return <TrendingUp className="w-4 h-4" />
      case 'completed':
        return <CheckCircle className="w-4 h-4" />
      case 'rejected':
      case 'cancelled':
        return <XCircle className="w-4 h-4" />
      default:
        return <Clock className="w-4 h-4" />
    }
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="text-white">جاري التحميل...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-900">
      {/* Header */}
      <header className="bg-gray-800 border-b border-gray-700">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-white">مرحباً، {user.name}</h1>
              <p className="text-gray-400">لوحة تحكم التاجر</p>
            </div>
            
            <Link href="/merchant/campaigns/create">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-medium py-3 px-6 rounded-xl flex items-center space-x-2 space-x-reverse"
              >
                <Plus className="w-5 h-5" />
                <span>حملة جديدة</span>
              </motion.button>
            </Link>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Stats Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="grid grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
        >
          <div className="bg-gray-800 rounded-2xl p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-blue-600/20 rounded-xl flex items-center justify-center">
                <BarChart3 className="w-6 h-6 text-blue-400" />
              </div>
            </div>
            <h3 className="text-2xl font-bold text-white mb-1">{stats.totalCampaigns}</h3>
            <p className="text-gray-400 text-sm">إجمالي الحملات</p>
          </div>

          <div className="bg-gray-800 rounded-2xl p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-yellow-600/20 rounded-xl flex items-center justify-center">
                <TrendingUp className="w-6 h-6 text-yellow-400" />
              </div>
            </div>
            <h3 className="text-2xl font-bold text-white mb-1">{stats.activeCampaigns}</h3>
            <p className="text-gray-400 text-sm">حملات نشطة</p>
          </div>

          <div className="bg-gray-800 rounded-2xl p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-green-600/20 rounded-xl flex items-center justify-center">
                <CheckCircle className="w-6 h-6 text-green-400" />
              </div>
            </div>
            <h3 className="text-2xl font-bold text-white mb-1">{stats.completedCampaigns}</h3>
            <p className="text-gray-400 text-sm">حملات مكتملة</p>
          </div>

          <div className="bg-gray-800 rounded-2xl p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-purple-600/20 rounded-xl flex items-center justify-center">
                <DollarSign className="w-6 h-6 text-purple-400" />
              </div>
            </div>
            <h3 className="text-2xl font-bold text-white mb-1">{formatCurrency(stats.totalSpent)}</h3>
            <p className="text-gray-400 text-sm">إجمالي الإنفاق</p>
          </div>
        </motion.div>

        {/* Recent Campaigns */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-800 rounded-2xl border border-gray-700"
        >
          <div className="p-6 border-b border-gray-700">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold text-white">الحملات الأخيرة</h2>
              <Link href="/merchant/campaigns">
                <button className="text-indigo-400 hover:text-indigo-300 text-sm font-medium">
                  عرض الكل
                </button>
              </Link>
            </div>
          </div>

          <div className="p-6">
            {isLoading ? (
              <div className="text-center py-8">
                <div className="text-gray-400">جاري التحميل...</div>
              </div>
            ) : campaigns.length === 0 ? (
              <div className="text-center py-8">
                <Users className="w-16 h-16 mx-auto mb-4 text-gray-600" />
                <h3 className="text-lg font-semibold text-gray-400 mb-2">لا توجد حملات بعد</h3>
                <p className="text-gray-500 mb-4">ابدأ أول حملة إعلانية لك</p>
                <Link href="/merchant/campaigns/create">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-xl"
                  >
                    إنشاء حملة
                  </motion.button>
                </Link>
              </div>
            ) : (
              <div className="space-y-4">
                {campaigns.slice(0, 5).map((campaign) => (
                  <div
                    key={campaign.id}
                    className="flex items-center justify-between p-4 bg-gray-700/50 rounded-xl hover:bg-gray-700/70 transition-colors duration-200"
                  >
                    <div className="flex items-center space-x-4 space-x-reverse">
                      <div className={`w-10 h-10 rounded-xl flex items-center justify-center ${getStatusColor(campaign.status).replace('text-', 'bg-').replace('400', '600/20')}`}>
                        {getStatusIcon(campaign.status)}
                      </div>
                      
                      <div>
                        <h3 className="font-semibold text-white">{campaign.title}</h3>
                        <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-400">
                          <span className={getStatusColor(campaign.status)}>
                            {campaignStatus[campaign.status as keyof typeof campaignStatus]}
                          </span>
                          <span className="flex items-center space-x-1 space-x-reverse">
                            <Calendar className="w-4 h-4" />
                            <span>{new Date(campaign.createdAt).toLocaleDateString('ar-SA')}</span>
                          </span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-4 space-x-reverse">
                      <div className="text-left">
                        <div className="text-lg font-bold text-white">{formatCurrency(campaign.budget)}</div>
                        <div className="text-sm text-gray-400">الميزانية</div>
                      </div>
                      
                      <Link href={`/merchant/campaigns/${campaign.id}`}>
                        <motion.button
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                          className="w-10 h-10 bg-gray-600 hover:bg-gray-500 rounded-xl flex items-center justify-center text-gray-300 hover:text-white"
                        >
                          <Eye className="w-5 h-5" />
                        </motion.button>
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </motion.div>

        {/* Quick Actions */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="grid md:grid-cols-3 gap-6 mt-8"
        >
          <Link href="/browse">
            <div className="bg-gray-800 rounded-2xl p-6 border border-gray-700 hover:border-gray-600 transition-colors duration-200 cursor-pointer">
              <div className="w-12 h-12 bg-indigo-600/20 rounded-xl flex items-center justify-center mb-4">
                <Users className="w-6 h-6 text-indigo-400" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">تصفح المؤثرين</h3>
              <p className="text-gray-400 text-sm">اكتشف المؤثرين المناسبين لعلامتك التجارية</p>
            </div>
          </Link>

          <Link href="/merchant/campaigns/create">
            <div className="bg-gray-800 rounded-2xl p-6 border border-gray-700 hover:border-gray-600 transition-colors duration-200 cursor-pointer">
              <div className="w-12 h-12 bg-green-600/20 rounded-xl flex items-center justify-center mb-4">
                <Plus className="w-6 h-6 text-green-400" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">حملة جديدة</h3>
              <p className="text-gray-400 text-sm">ابدأ حملة إعلانية جديدة مع المؤثرين</p>
            </div>
          </Link>

          <Link href="/merchant/analytics">
            <div className="bg-gray-800 rounded-2xl p-6 border border-gray-700 hover:border-gray-600 transition-colors duration-200 cursor-pointer">
              <div className="w-12 h-12 bg-purple-600/20 rounded-xl flex items-center justify-center mb-4">
                <BarChart3 className="w-6 h-6 text-purple-400" />
              </div>
              <h3 className="text-lg font-semibold text-white mb-2">التحليلات</h3>
              <p className="text-gray-400 text-sm">تتبع أداء حملاتك الإعلانية</p>
            </div>
          </Link>
        </motion.div>
      </div>
    </div>
  )
}
