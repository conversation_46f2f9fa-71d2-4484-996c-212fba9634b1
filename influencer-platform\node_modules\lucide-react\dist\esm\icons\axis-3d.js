/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M13.5 10.5 15 9", key: "1nsxvm" }],
  ["path", { d: "M4 4v15a1 1 0 0 0 1 1h15", key: "1w6lkd" }],
  ["path", { d: "M4.293 19.707 6 18", key: "3g1p8c" }],
  ["path", { d: "m9 15 1.5-1.5", key: "1xfbes" }]
];
const Axis3d = createLucideIcon("axis-3d", __iconNode);

export { __iconNode, Axis3d as default };
//# sourceMappingURL=axis-3d.js.map
